<!DOCTYPE html>
<html>
<head>
    <title>CCTV Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CCTV Camera Management</h1>
            <a href="{{ url_for('logout') }}" class="logout-btn">Logout</a>
        </div>

        {% if net is none %}
        <div class="alert alert-warning">
            <strong>Warning:</strong> Object detection model is not loaded. Person detection is disabled.
            Please run <code>python download_models.py</code> to download the required model files.
        </div>
        {% endif %}

        <div class="top-panels">
            <div class="add-camera-form">
                <h2>Add New Camera</h2>
                <form action="{{ url_for('add_camera') }}" method="post">
                    <div class="form-group">
                        <label for="camera_name">Camera Name:</label>
                        <input type="text" id="camera_name" name="camera_name" required>
                    </div>
                    <div class="form-group">
                        <label for="camera_url">RTSP URL:</label>
                        <input type="text" id="camera_url" name="camera_url" required>
                    </div>
                    <button type="submit" class="btn">Add Camera</button>
                </form>
            </div>

            <div class="notification-panel">
                <h2>Notification Settings</h2>
                <p>Configure recipients for camera detection alerts</p>
                <div class="recipients-summary">
                    <h3>Recipients</h3>
                    <div id="recipients-summary-container">
                        <p id="no-recipients-summary">No recipients configured yet.</p>
                    </div>
                </div>
                <a href="{{ url_for('manage_recipients') }}" class="btn notification-btn">Manage Recipients</a>
            </div>
        </div>

        <div class="camera-section">
            <h2 class="section-heading">Your Cameras</h2>
            <div class="camera-list">
                {% if cameras %}
                    <div class="camera-grid">
                        {% for camera_id, camera in cameras.items() %}
                            <div class="camera-card">
                                <div class="camera-header">
                                    <h3>{{ camera.name }}</h3>
                                    <a href="{{ url_for('view_history', camera_id=camera_id) }}" class="view-history-btn">View History</a>
                                </div>
                                <div class="camera-feed">
                                    <img src="{{ url_for('video_feed', camera_id=camera_id) }}" alt="{{ camera.name }}"
                                         class="camera-stream" data-camera-id="{{ camera_id }}"
                                         onerror="handleStreamError(this)">
                                </div>
                                <div class="camera-status">
                                    <div class="status-indicator active">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Detection: On</span>
                                    </div>
                                    <div class="status-indicator {% if camera.notification_status %}active{% endif %}">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Alerts: {{ 'On' if camera.notification_status else 'Off' }}</span>
                                    </div>
                                </div>
                                <div class="card-actions">
                                    <div class="action-row">
                                        <form action="{{ url_for('toggle_notifications', camera_id=camera_id) }}" method="post" class="action-form">
                                            <button type="submit" class="toggle-btn notification-toggle {% if camera.notification_status %}active{% endif %}">
                                                {{ 'Stop Alerts' if camera.notification_status else 'Start Alerts' }}
                                            </button>
                                        </form>
                                    </div>
                                    <div class="action-row">
                                        <form action="{{ url_for('remove_camera', camera_id=camera_id) }}" method="post" class="action-form">
                                            <button type="submit" class="remove-btn">Remove Camera</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="no-cameras-container">
                        <p class="no-cameras-message">No cameras added yet. Add your first camera above.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>

<script>
    // Handle stream errors and improve video quality
    function handleStreamError(img) {
        console.log('Stream error for camera:', img.dataset.cameraId);

        // Set fallback image
        img.src = '{{ url_for("static", filename="img/camera_offline.jpg") }}';

        // Try to reconnect after a shorter delay for faster recovery
        setTimeout(() => {
            const cameraId = img.dataset.cameraId;
            const newSrc = `/video_feed/${cameraId}?t=${Date.now()}`;

            console.log('Attempting to reconnect camera:', cameraId);
            img.src = newSrc;
        }, 2000); // Reduced to 2 seconds for faster recovery
    }

    // Improve video streaming performance
    function optimizeVideoStreams() {
        const streams = document.querySelectorAll('.camera-stream');

        streams.forEach(stream => {
            // Add loading event handler
            stream.addEventListener('load', function() {
                console.log('Stream loaded for camera:', this.dataset.cameraId);
            });

            // Add error event handler with retry logic
            stream.addEventListener('error', function() {
                handleStreamError(this);
            });

            // Periodically refresh streams to prevent lag buildup and stale connections
            setInterval(() => {
                if (!stream.src.includes('camera_offline.jpg')) {
                    const currentSrc = stream.src.split('?')[0];
                    stream.src = `${currentSrc}?t=${Date.now()}`;
                }
            }, 120000); // Refresh every 2 minutes to prevent lag buildup
        });
    }

    // Load recipients summary when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize video stream optimization
        optimizeVideoStreams();

        // Load recipients summary
        fetch('/get_recipients')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recipients-summary-container');
            const noRecipientsMsg = document.getElementById('no-recipients-summary');

            if (data.recipients && data.recipients.length > 0) {
                // Hide no recipients message
                noRecipientsMsg.style.display = 'none';

                // Create recipient summary
                const count = data.recipients.length;
                const summary = document.createElement('p');
                summary.textContent = `${count} recipient${count > 1 ? 's' : ''} configured`;
                container.appendChild(summary);
            } else {
                // Show no recipients message
                noRecipientsMsg.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });
</script>















