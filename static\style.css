body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
}

h1, h2, h3 {
    color: #333;
}

/* Header styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 25px 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    margin: 0;
    text-align: left;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 32px;
    font-weight: 700;
}

.logout-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Top panels layout */
.top-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.add-camera-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.add-camera-form h2 {
    margin-top: 0;
    margin-bottom: 25px;
    color: #333;
    font-size: 22px;
    font-weight: 600;
}

.notification-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-panel h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 22px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.notification-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.notification-btn:hover {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

@media (max-width: 768px) {
    .top-panels {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* Camera section styles */
.camera-section {
    margin-top: 30px;
    width: 100%;
    clear: both;
}

.section-heading {
    margin-bottom: 15px;
    color: #333;
    font-size: 22px;
    padding: 10px 0;
    border-bottom: 2px solid #eee;
    width: 100%;
    text-align: left;
}

.camera-list {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px;
    width: 100%;
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.camera-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all 0.3s ease;
    min-width: 350px;
    max-width: 100%;
}

.camera-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.camera-header h3 {
    margin: 0;
    text-align: left;
    font-size: 18px;
    color: #333;
}

.view-history-btn {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
}

.view-history-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(142, 68, 173, 0.4);
}

.camera-feed {
    width: 100%;
    height: 0;
    padding-bottom: 75%; /* 4:3 aspect ratio */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.camera-feed img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 15px;
    transition: opacity 0.3s ease;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Loading state for camera streams */
.camera-feed img[src*="camera_offline.jpg"] {
    opacity: 0.7;
    filter: grayscale(100%);
}

/* Smooth loading transition */
.camera-feed img:not([src*="camera_offline.jpg"]) {
    opacity: 1;
    filter: none;
}

/* Add a subtle loading indicator */
.camera-feed::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-feed.loading::before {
    opacity: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.card-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.remove-btn {
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
}

.remove-btn:hover {
    background-color: #d32f2f;
}

/* Camera status indicators */
.camera-status {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    padding: 5px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.status-indicator {
    display: flex;
    align-items: center;
    color: #777;
    font-size: 14px;
}

.status-indicator.active {
    color: #4CAF50;
}

.status-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ccc;
    margin-right: 5px;
}

.status-indicator.active .status-dot {
    background-color: #4CAF50;
}

/* Action buttons */
.card-actions {
    margin-top: 15px;
}

.action-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.action-row:last-child {
    margin-bottom: 0;
}

.action-form {
    flex: 1;
    margin: 0 5px;
}

.action-form:first-child {
    margin-left: 0;
}

.action-form:last-child {
    margin-right: 0;
}

.toggle-btn {
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(158, 158, 158, 0.3);
}

.toggle-btn.active {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(158, 158, 158, 0.4);
}

.toggle-btn.active:hover {
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.notification-toggle {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.notification-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.notification-toggle.active {
    background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.notification-toggle.active:hover {
    box-shadow: 0 8px 25px rgba(255, 87, 34, 0.4);
}

.remove-btn {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.remove-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
}

/* Message when no cameras are added */
.no-cameras-container {
    width: 100%;
}

.no-cameras-message {
    color: #666;
    font-style: italic;
    margin: 0;
    text-align: left;
}

/* Responsive layout for camera grid */
@media (max-width: 1200px) {
    .camera-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
}

@media (max-width: 900px) {
    .camera-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .camera-card {
        min-width: 300px;
        padding: 20px;
    }

    .container {
        padding: 20px 15px;
    }

    .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header h1 {
        font-size: 24px;
    }

    .add-camera-form,
    .notification-panel {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    .top-panels {
        grid-template-columns: 1fr;
    }
}

/* History page styles */
/* Improved history header */
.history-header {
    margin-bottom: 30px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.history-header h1 {
    margin-top: 0;
    color: #333;
    font-size: 24px;
    margin-bottom: 15px;
}

.history-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.date-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.date-selector label {
    font-weight: 600;
    color: #555;
}

.date-selector input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #45a049;
}

.back-btn {
    background-color: #f8f8f8;
    color: #333;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-weight: 600;
    transition: all 0.3s;
}

.back-btn:hover {
    background-color: #eee;
    border-color: #ccc;
}

.event-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.event-card {
    background-color: white;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.event-time {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.event-image img {
    width: 100%;
    height: auto;
    border-radius: 3px;
}

.no-events {
    text-align: center;
    padding: 30px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@media (max-width: 992px) {
    .event-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .event-grid {
        grid-template-columns: 1fr;
    }
}

/* Add styles for charts */
/* Updated chart styles */
.charts-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    padding-top: 20px; /* Add padding at the top */
}

.chart-card {
    background-color: white;
    border-radius: 8px;
    padding: 30px 20px; /* Increase top padding */
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.full-width {
    width: 100%;
    max-width: 650px; /* Slightly smaller max-width */
}

.chart-card h2 {
    text-align: center;
    margin-top: 0;
    margin-bottom: 25px; /* Increase bottom margin */
    font-size: 20px;
    color: #333;
    font-weight: 600;
}

.chart-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 550px; /* Slightly smaller height */
}

#minuteChart {
    max-width: 550px; /* Smaller max-width */
    max-height: 550px; /* Smaller max-height */
    margin: 0 auto;
    display: block;
}

/* Ensure canvas elements take up full space */
.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}

/* Improved date selector */
.date-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-selector label {
    font-weight: 600;
    color: #555;
}

.date-selector input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #45a049;
}

.back-btn {
    background-color: #2196F3;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s;
}

.back-btn:hover {
    background-color: #0b7dda;
}

@media (max-width: 992px) {
    .charts-container {
        grid-template-columns: 1fr;
    }
}

/* Layout for top panels */
.top-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
}

.notification-panel {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    height: 100%;
}

.add-camera-form {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    height: 100%;
}

.notification-btn {
    background-color: #25D366;
    color: white;
}

.notification-btn:hover {
    background-color: #128C7E;
}

.recipients-summary {
    margin-top: 15px;
}

.recipients-summary h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #555;
}

#no-recipients-summary {
    color: #888;
    font-style: italic;
}

/* Responsive layout */
@media (max-width: 768px) {
    .top-panels {
        grid-template-columns: 1fr;
    }
}

/* WhatsApp Notification Styles */
.notification-panel {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.notification-btn {
    background-color: #25D366;
    color: white;
}

.notification-btn:hover {
    background-color: #128C7E;
}

.recipients-list {
    margin-top: 20px;
}

.recipients-list h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #555;
}

.recipients-list-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recipient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.recipient-item:last-child {
    border-bottom: none;
}

.recipient-info {
    font-size: 14px;
}

/* Recipient actions */
.recipient-actions {
    display: flex;
    gap: 10px;
}

.test-notification-btn {
    background-color: #25D366;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
}

.test-notification-btn:hover {
    background-color: #128C7E;
}

.delete-recipient-btn {
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
}

.delete-recipient-btn:hover {
    background-color: #d32f2f;
}

/* Loading indicator */
.loading-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 5px;
    z-index: 1000;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    width: 80%;
    max-width: 500px;
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover,
.close-modal:focus {
    color: black;
    text-decoration: none;
}

#no-recipients-msg {
    color: #888;
    font-style: italic;
}

/* Recipient Management Page Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.recipient-management {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
    margin-top: 60px;
}

.add-recipient-form {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.recipients-list-container {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.recipients-summary {
    margin-top: 15px;
}

.recipients-summary h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #555;
}

#no-recipients-summary {
    color: #888;
    font-style: italic;
}

@media (max-width: 768px) {
    .recipient-management {
        grid-template-columns: 1fr;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .header .back-btn {
        margin-top: 15px;
    }
}

/* Phone input with country code */
.phone-input-container {
    display: flex;
    align-items: center;
}

.country-code {
    background-color: #f5f5f5;
    padding: 8px 10px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333;
    font-weight: bold;
}

#recipient-phone {
    border-radius: 0 4px 4px 0;
    flex: 1;
}

.form-hint {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

/* Toast notification styles */
.toast-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    background-color: #4CAF50;
    color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast-notification.error {
    background-color: #f44336;
}

.toast-notification.warning {
    background-color: #ff9800;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .top-panels {
        grid-template-columns: 1fr;
    }

    .camera-section {
        margin-top: 20px;
    }

    .section-heading {
        font-size: 20px;
    }
}

.notification-settings {
    margin-bottom: 60px;
}

/* Notification settings grid layout */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 60px;
}

.settings-card {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    height: 100%;
}

/* Make sure the cards have equal height */
.settings-card h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

/* Responsive layout - switch to single column on small screens */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

.settings-card p {
    color: #666;
    margin-bottom: 15px;
}

#cooldown-minutes {
    width: 80px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

#interval-hours, #interval-minutes {
    width: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    margin-right: 10px;
}

.form-group label {
    margin-right: 5px;
    display: inline-block;
}

.form-hint {
    display: block;
    margin-top: 5px;
    color: #666;
    font-style: italic;
}









