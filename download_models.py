import os
import urllib.request

# Create models directory if it doesn't exist
if not os.path.exists('models'):
    os.makedirs('models')

# Download MobileNet SSD model files
prototxt_url = "https://raw.githubusercontent.com/chuanqi305/MobileNet-SSD/master/deploy.prototxt"
model_url = "https://drive.google.com/uc?export=download&id=0B3gersZ2cHIxRm5PMWRoTkdHdHc"

# Download prototxt file
prototxt_path = 'models/MobileNetSSD_deploy.prototxt'
if not os.path.exists(prototxt_path):
    print(f"Downloading {prototxt_path}...")
    urllib.request.urlretrieve(prototxt_url, prototxt_path)
    print("Done!")

# For the caffemodel, you'll need to download it manually from:
# https://drive.google.com/file/d/0B3gersZ2cHIxRm5PMWRoTkdHdHc/view
print("Please download the caffemodel file manually from:")
print("https://drive.google.com/file/d/0B3gersZ2cHIxRm5PMWRoTkdHdHc/view")
print(f"And save it to: {os.path.abspath('models/MobileNetSSD_deploy.caffemodel')}")