{"notifications": [{"timestamp": "2025-05-20 12:41:47", "message": "Alert! 1 person(s) detected on camera 'opl' at 2025-05-20 12:41:47", "recipients": [{"id": "52ea7b96-68be-44d5-941b-017ebd19c0a4", "name": "lizard", "phone": "+1234567823"}]}, {"timestamp": "2025-05-20 12:41:49", "message": "Alert! 1 person(s) detected on camera 'opl' at 2025-05-20 12:41:49", "recipients": [{"id": "52ea7b96-68be-44d5-941b-017ebd19c0a4", "name": "lizard", "phone": "+1234567823"}]}, {"timestamp": "2025-05-20 14:46:00", "message": "TEST: Hello <PERSON>, this is a test notification from your CCTV Camera Management System.", "recipients": [{"id": "0fa757df-2895-4a7b-aff7-2981fe428212", "name": "<PERSON>", "phone": "+917039528824", "added_on": "2025-05-20 14:45:50"}]}, {"timestamp": "2025-05-20 14:52:04", "message": "TEST: Hello <PERSON>, this is a test notification from your CCTV Camera Management System.", "recipients": [{"id": "0fa757df-2895-4a7b-aff7-2981fe428212", "name": "<PERSON>", "phone": "+917039528824", "added_on": "2025-05-20 14:45:50"}]}, {"timestamp": "2025-05-20 14:52:34", "message": "TEST: Hello <PERSON>, this is a test notification from your CCTV Camera Management System.", "recipients": [{"id": "0fa757df-2895-4a7b-aff7-2981fe428212", "name": "<PERSON>", "phone": "+917039528824", "added_on": "2025-05-20 14:45:50"}]}, {"timestamp": "2025-05-20 15:05:48", "message": "TEST EMAIL: Hello <PERSON>,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "0fa757df-2895-4a7b-aff7-2981fe428212", "name": "<PERSON>", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 14:45:50"}]}, {"timestamp": "2025-05-20 15:30:46", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:31:36", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:42:20", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:44:27", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:44:36", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:49:00", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 15:52:47", "message": "Digest email: 10 detection events sent to lizard", "recipients": [{"id": "52ea7b96-68be-44d5-941b-017ebd19c0a4", "name": "lizard", "phone": "+1234567823", "email": "<EMAIL>", "added_on": "2025-05-20 12:23:24"}]}, {"timestamp": "2025-05-20 15:52:50", "message": "Digest email: 10 detection events sent to <PERSON>", "recipients": [{"id": "0fa757df-2895-4a7b-aff7-2981fe428212", "name": "<PERSON>", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 14:45:50"}]}, {"timestamp": "2025-05-20 15:52:53", "message": "Digest email: 10 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 16:17:00", "message": "Digest email: 13 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 16:21:24", "message": "Digest email: 47 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 16:25:15", "message": "Digest email: 14 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-20 16:30:43", "message": "Digest email: 39 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 13:02:37", "message": "Digest email: 29 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 15:28:36", "message": "Digest email: 1 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 15:30:42", "message": "Digest email: 2 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 15:55:58", "message": "Digest email: 1 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 17:10:02", "message": "Digest email: 2 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-21 17:13:09", "message": "Digest email: 2 detection events sent to asd123", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}, {"timestamp": "2025-05-22 11:29:16", "message": "TEST EMAIL: Hello asd123,\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System", "recipients": [{"id": "3528ac55-2530-4fa2-8cb5-73984f595093", "name": "asd123", "phone": "+917039528824", "email": "<EMAIL>", "added_on": "2025-05-20 15:30:36"}]}]}